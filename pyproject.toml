[project]
name = "epidemiology-research-system"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "agno==2.0.4",
    "bioc>=2.1",
    "biomcp-python>=0.6.8",
    "docling>=2.51.0",
    "openai>=1.106.1",
    "pandas>=2.3.2",
    "pymysql",
    "python-dotenv",
    "pyyaml>=6.0.2",
    "reflex>=0.8.10",
    "requests>=2.32.5",
    "sqlalchemy",
    "unstructured[all-docs]>=0.18.14",
]

[dependency-groups]
dev = [
    "ipykernel>=6.30.1",
    "pytest>=8.4.2",
]
