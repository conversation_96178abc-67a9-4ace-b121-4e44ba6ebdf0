"""
Epidemiology Research System - Streamlit Web Application

This is the main entry point for the Streamlit web application that replaces
the CLI interface with an interactive user experience for epidemiology research.

The application provides two main pages:
1. Epidemiology Research Interface - For conducting research with real-time progress tracking
2. Data Visualization Dashboard - For displaying and filtering extracted data
"""

import streamlit as st
import sys
import os

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from dotenv import load_dotenv
load_dotenv()

# Configure the Streamlit page
st.set_page_config(
    page_title="Epidemiology Research System",
    page_icon="🔬",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Import page modules
from streamlit_app.pages.research_interface import research_interface_page
from streamlit_app.pages.data_dashboard import data_dashboard_page
from streamlit_app.utils.session_state import initialize_session_state
from streamlit_app.utils.database import check_database_connection

def main():
    """Main application function with navigation and page routing."""
    
    # Initialize session state
    initialize_session_state()
    
    # Check database connection on startup
    if "db_connection_checked" not in st.session_state:
        with st.spinner("Checking database connection..."):
            try:
                db_status = check_database_connection()
                st.session_state.db_connection_checked = True
                st.session_state.db_connection_status = db_status
            except Exception as e:
                st.session_state.db_connection_checked = True
                st.session_state.db_connection_status = {"connected": False, "error": str(e)}

    # Display database connection status
    if not st.session_state.db_connection_status["connected"]:
        st.error(f"❌ Database Connection Failed: {st.session_state.db_connection_status['error']}")
        st.info("Please check your database configuration in .env file or environment variables")
        st.info("See database_config.example for required settings")

        # Allow users to continue to dashboard even without database connection
        st.warning("⚠️ Some features may not work without database connection")

        # Still show navigation but with limited functionality
        st.sidebar.title("🔬 Epidemiology Research System")
        st.sidebar.markdown("---")
        st.sidebar.error("Database connection required for full functionality")

        # Only show dashboard page when DB is down
        if st.sidebar.button("📊 View Dashboard (Limited)"):
            st.session_state.current_page = "dashboard"

        if st.session_state.get("current_page") == "dashboard":
            st.warning("Dashboard functionality is limited without database connection")
            data_dashboard_page()
        else:
            st.info("Please fix database connection to access research interface")
        return
    else:
        # Only show success message briefly
        if not st.session_state.get("db_success_shown", False):
            st.success("✅ Database connection established")
            st.session_state.db_success_shown = True
    
    # Sidebar navigation
    st.sidebar.title("🔬 Epidemiology Research System")
    st.sidebar.markdown("---")

    # Navigation menu with icons and descriptions
    st.sidebar.markdown("### 🧭 Navigation")

    # Create navigation buttons instead of selectbox for better UX
    col1, col2 = st.sidebar.columns(2)

    with col1:
        if st.button("🔍 Research", use_container_width=True,
                    type="primary" if st.session_state.get("current_page") == "research" else "secondary"):
            st.session_state.current_page = "research"
            st.rerun()

    with col2:
        if st.button("📊 Dashboard", use_container_width=True,
                    type="primary" if st.session_state.get("current_page") == "dashboard" else "secondary"):
            st.session_state.current_page = "dashboard"
            st.rerun()

    # Add descriptions
    st.sidebar.markdown("""
    **🔍 Research Interface**
    Conduct epidemiology research with real-time progress tracking

    **📊 Data Dashboard**
    Visualize and analyze extracted epidemiological data
    """)

    # Add some spacing
    st.sidebar.markdown("---")

    # Display current session info in sidebar
    if "research_progress" in st.session_state and st.session_state.research_progress:
        st.sidebar.markdown("### 📈 Current Research")
        progress_info = st.session_state.research_progress
        st.sidebar.write(f"**Disease:** {progress_info.get('disease_name', 'N/A')}")
        st.sidebar.write(f"**Country:** {progress_info.get('country', 'N/A')}")
        st.sidebar.write(f"**Status:** {progress_info.get('current_step', 'Idle')}")

        if progress_info.get('is_running', False):
            st.sidebar.warning("🔄 Research in progress...")
        elif progress_info.get('current_step') == 'Completed':
            st.sidebar.success("✅ Research completed!")

    # Add system information
    st.sidebar.markdown("---")
    st.sidebar.markdown("### ℹ️ System Info")
    st.sidebar.caption(f"Database: {'✅ Connected' if st.session_state.db_connection_status['connected'] else '❌ Disconnected'}")
    st.sidebar.caption(f"Session: {st.session_state.get('session_id', 'Unknown')[:8]}...")

    # Add help section
    with st.sidebar.expander("❓ Help & Tips"):
        st.markdown("""
        **Getting Started:**
        1. Use Research Interface to analyze diseases
        2. View results in Data Dashboard
        3. Filter data by disease and country

        **Tips:**
        - Research runs synchronously for reliability
        - Dashboard supports sample data for testing
        - All progress is saved in session state
        """)

    # Initialize session ID if not exists
    if "session_id" not in st.session_state:
        import uuid
        st.session_state.session_id = str(uuid.uuid4())
    
    # Route to the selected page
    if st.session_state.current_page == "research":
        research_interface_page()
    elif st.session_state.current_page == "dashboard":
        data_dashboard_page()

if __name__ == "__main__":
    main()
