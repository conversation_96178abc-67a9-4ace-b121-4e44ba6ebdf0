#!/usr/bin/env python3
"""
Test script for the Streamlit Epidemiology Research Application

This script tests the core functionality of the Streamlit application
including imports, database connections, and data generation.
"""

import sys
import os
import traceback

def test_imports():
    """Test that all required modules can be imported."""
    print("🔍 Testing imports...")
    
    try:
        import streamlit as st
        print("✅ Streamlit imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import Streamlit: {e}")
        return False
    
    try:
        import pandas as pd
        print("✅ Pandas imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import Pandas: {e}")
        return False
    
    try:
        import plotly.express as px
        print("✅ Plotly imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import Plotly: {e}")
        return False
    
    try:
        from streamlit_app.utils.session_state import initialize_session_state
        print("✅ Session state utilities imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import session state utilities: {e}")
        return False
    
    try:
        from streamlit_app.utils.database import check_database_connection
        print("✅ Database utilities imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import database utilities: {e}")
        return False
    
    try:
        from streamlit_app.utils.sample_data import generate_sample_prevalence_data
        print("✅ Sample data utilities imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import sample data utilities: {e}")
        return False
    
    try:
        from streamlit_app.pages.research_interface import research_interface_page
        print("✅ Research interface page imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import research interface page: {e}")
        return False
    
    try:
        from streamlit_app.pages.data_dashboard import data_dashboard_page
        print("✅ Data dashboard page imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import data dashboard page: {e}")
        return False
    
    return True

def test_sample_data():
    """Test sample data generation."""
    print("\n📊 Testing sample data generation...")
    
    try:
        from streamlit_app.utils.sample_data import (
            generate_sample_prevalence_data,
            generate_sample_incidence_data,
            get_sample_summary_stats,
            get_sample_diseases,
            get_sample_countries
        )
        
        # Test prevalence data generation
        point_df, period_df = generate_sample_prevalence_data()
        print(f"✅ Generated {len(point_df)} point prevalence records")
        print(f"✅ Generated {len(period_df)} period prevalence records")
        
        # Test incidence data generation
        rate_df, cumulative_df = generate_sample_incidence_data()
        print(f"✅ Generated {len(rate_df)} incidence rate records")
        print(f"✅ Generated {len(cumulative_df)} cumulative incidence records")
        
        # Test summary stats
        stats = get_sample_summary_stats()
        print(f"✅ Generated summary statistics: {stats}")
        
        # Test disease and country lists
        diseases = get_sample_diseases()
        countries = get_sample_countries()
        print(f"✅ Sample diseases: {len(diseases)} items")
        print(f"✅ Sample countries: {len(countries)} items")
        
        return True
        
    except Exception as e:
        print(f"❌ Sample data generation failed: {e}")
        traceback.print_exc()
        return False

def test_database_connection():
    """Test database connection (may fail if not configured)."""
    print("\n🗄️ Testing database connection...")
    
    try:
        from streamlit_app.utils.database import check_database_connection
        
        db_status = check_database_connection()
        if db_status["connected"]:
            print("✅ Database connection successful")
        else:
            print(f"⚠️ Database connection failed (expected): {db_status['error']}")
        
        return True
        
    except Exception as e:
        print(f"⚠️ Database connection test failed (expected): {e}")
        return True  # This is expected if database is not configured

def test_session_state():
    """Test session state management."""
    print("\n🔄 Testing session state management...")
    
    try:
        from streamlit_app.utils.session_state import (
            initialize_session_state,
            update_research_progress,
            set_research_running,
            set_research_completed
        )
        
        # Create a mock session state
        class MockSessionState:
            def __init__(self):
                self.data = {}
            
            def __getitem__(self, key):
                return self.data[key]
            
            def __setitem__(self, key, value):
                self.data[key] = value
            
            def get(self, key, default=None):
                return self.data.get(key, default)
            
            def __contains__(self, key):
                return key in self.data
        
        # Test initialization (would normally use st.session_state)
        print("✅ Session state functions imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Session state test failed: {e}")
        traceback.print_exc()
        return False

def test_file_structure():
    """Test that all required files exist."""
    print("\n📁 Testing file structure...")
    
    required_files = [
        "app.py",
        "streamlit_app/__init__.py",
        "streamlit_app/utils/__init__.py",
        "streamlit_app/utils/session_state.py",
        "streamlit_app/utils/database.py",
        "streamlit_app/utils/sample_data.py",
        "streamlit_app/pages/__init__.py",
        "streamlit_app/pages/research_interface.py",
        "streamlit_app/pages/data_dashboard.py",
        "streamlit_app/components/__init__.py",
        "streamlit_app/components/progress_tracker.py"
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - Missing!")
            all_exist = False
    
    return all_exist

def main():
    """Run all tests."""
    print("🧪 Testing Streamlit Epidemiology Research Application")
    print("=" * 60)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Imports", test_imports),
        ("Sample Data", test_sample_data),
        ("Database Connection", test_database_connection),
        ("Session State", test_session_state)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("📋 Test Results Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! The application is ready to use.")
        return 0
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
