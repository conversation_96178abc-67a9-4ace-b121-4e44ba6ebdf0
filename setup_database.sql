-- Setup script for epidemiology research database
-- This script creates the database and user as specified in the .env file

-- Create the database
CREATE DATABASE IF NOT EXISTS epidemiology_research;

-- Create the user and grant privileges
CREATE USER IF NOT EXISTS 'epidemiology_user'@'localhost' IDENTIFIED BY 'EpiResearch2024!';
GRANT ALL PRIVILEGES ON epidemiology_research.* TO 'epidemiology_user'@'localhost';

-- Also grant privileges for connections from any host (for flexibility)
CREATE USER IF NOT EXISTS 'epidemiology_user'@'%' IDENTIFIED BY 'EpiResearch2024!';
GRANT ALL PRIVILEGES ON epidemiology_research.* TO 'epidemiology_user'@'%';

-- Flush privileges to ensure changes take effect
FLUSH PRIVILEGES;

-- Show the created database
SHOW DATABASES;

-- Show the created users
SELECT User, Host FROM mysql.user WHERE User = 'epidemiology_user';
